<?php

namespace Database\Seeders;

use App\Models\Program;
use Illuminate\Database\Seeder;

class ProgramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Program::factory()->create([
            'name' => 'General Donation',
            'description' => 'Description for Program 1',
            'transaction_code' => 0,
            'status' => 'ongoing',
        ]);

        // Create draft programs (only visible to admin)
        Program::factory(3)->create([
            'status' => 'draft'
        ]);

        // Create ongoing programs
        Program::factory(5)->create([
            'status' => 'ongoing'
        ]);

        // Create completed programs
        Program::factory(3)->create([
            'status' => 'done',
            'achievement' => function (array $attributes) {
                // Set achievement to a percentage of the target (80-100%)
                return (int)($attributes['target'] * (rand(80, 100) / 100));
            }
        ]);

        // Create archived programs (only visible to admin)
        Program::factory(2)->create([
            'status' => 'archive',
            'achievement' => function (array $attributes) {
                // Set achievement to a percentage of the target (80-100%)
                return (int)($attributes['target'] * (rand(80, 100) / 100));
            }
        ]);
    }
}
