@extends('layouts.app')

@section('title', 'Program Management')
@section('panel-type', 'Program Panel')

@section('content')
<div x-data="programsManagement()"
     x-cloak
     @view-program.window="viewProgram($event.detail.id)"
     @edit-program.window="editProgram($event.detail.id)"
     @delete-program.window="confirmDelete($event.detail.id)">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Program Management</h2>
        @if(auth()->user()->isAdmin())
        <button @click="openCreateDialog()" class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 cursor-pointer">
            New
        </button>
        @endif
    </div>

    <!-- Filter Form -->
    @include('dashboard.programs.filter')

    <!-- Programs Table with Loading Overlay -->
    <div class="relative">
        <div x-show="loading"
             class="absolute inset-0 backdrop-blur-xs flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="text-gray-500 text-sm">Loading...</span>
            </div>
        </div>
        <div id="programs-table" x-ref="programsTable">
            @include('dashboard.programs.table')
        </div>
    </div>

    <!-- Create/Edit Dialog -->
    @include('dashboard.programs.form')

    <!-- View Dialog -->
    @include('dashboard.programs.view')

    <!-- Delete Confirmation Modal -->
    <x-delete-confirmation 
        showVariable="showDeleteDialog"
        title="Confirm Deletion"
        message="Are you sure you want to delete this program? This action cannot be undone."
        deleteMethod="deleteProgram()"
        cancelMethod="showDeleteDialog = false"
    />
</div>

<script>
function programsManagement() {
    return {
        showDialog: false,
        showViewDialog: false,
        showDeleteDialog: false,
        dialogTitle: '',
        loading: false,
        currentPageUrl: '',
        formData: {
            id: null,
            name: '',
            description: '',
            transaction_code: '',
            target: '',
            end_at: '',
            achievement: '',
            image: null,
            image_url: null
        },
        viewData: {},
        filters: {
            name: '',
            transaction_code: '',
            status: ''
        },
        errors: {},
        selectedFile: null,
        currentProgramId: null,

        init() {
            this.setupPagination();
            // Initialize the current page URL
            this.currentPageUrl = window.location.pathname + window.location.search;

            // Initialize filters from URL parameters if they exist
            const urlParams = new URLSearchParams(window.location.search);
            this.filters.name = urlParams.get('name') || '';

            // Handle transaction_code as a number
            const transactionCode = urlParams.get('transaction_code');
            this.filters.transaction_code = transactionCode !== null ? parseInt(transactionCode, 10) : '';

            this.filters.status = urlParams.get('status') || '';

            window.addEventListener('close-all-dialogs', () => {
                this.closeDialog();
                this.showViewDialog = false;
            });
        },

        setupPagination() {
            document.addEventListener('click', (e) => {
                const element = e.target.closest('#pagination-links a');
                if (element) {
                    e.preventDefault();
                    this.loadPage(element.href);
                }
            });
        },

        async loadPage(url) {
            try {
                this.loading = true;
                // Store the current page URL
                this.currentPageUrl = url;

                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();

                if (!document.startViewTransition) {
                    this.$refs.programsTable.innerHTML = html;
                    Alpine.initTree(this.$refs.programsTable);
                    return;
                }

                document.startViewTransition(() => {
                    this.$refs.programsTable.innerHTML = html;
                    Alpine.initTree(this.$refs.programsTable);
                });
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },

        formatCurrency(input, field) {
            window.formatCurrencyInput(input, field, this.formData);
        },

        // Helper method to format date from ISO format to YYYY-MM-DD for input[type=date]
        formatDateForInput(isoDate) {
            return window.formatDateForInput(isoDate);
        },

        openCreateDialog() {
            this.dialogTitle = 'New Program';
            this.formData = {
                id: null,
                name: '',
                description: '',
                transaction_code: '', // Default to 0
                target: '',
                end_at: '',
                achievement: 0,
                status: '{{ auth()->user()->isAdmin() ? "draft" : "ongoing" }}',
                image: null,
                image_url: null
            };
            this.errors = {};
            this.selectedFile = null;

            // Reset file input
            if (this.$refs.fileInput) {
                this.$refs.fileInput.value = '';
            }

            this.showDialog = true;
        },

        async viewProgram(id) {
            try {
                const response = await fetch(`/program/${id}`);
                if (!response.ok) throw new Error('Failed to fetch program');

                const data = await response.json();
                this.viewData = data;
                this.showViewDialog = true;
            } catch (error) {
                console.error('Error fetching program:', error);
                alert('Failed to load program details');
            }
        },

        async editProgram(id) {
            this.showViewDialog = false;

            try {
                const response = await fetch(`/program/${id}`);
                if (!response.ok) throw new Error('Failed to fetch program');

                const data = await response.json();
                this.formData = {
                    id: data.id,
                    name: data.name,
                    description: data.description,
                    transaction_code: data.transaction_code,
                    target: data.target || '',
                    end_at: window.formatDateForInput(data.end_at),
                    achievement: 0, // Always set to 0, will be populated later
                    status: data.status,
                    image: null,
                    image_url: data.image_url
                };
                this.dialogTitle = 'Edit Program';
                this.errors = {};
                this.selectedFile = null;

                // Reset file input
                if (this.$refs.fileInput) {
                    this.$refs.fileInput.value = '';
                }

                this.showDialog = true;
            } catch (error) {
                console.error('Error fetching program:', error);
                alert('Failed to load program details');
            }
        },

        handleFileChange(event) {
            this.selectedFile = event.target.files[0];
            this.errors.image = null;
            
            // Validate file size
            if (this.selectedFile && this.selectedFile.size > 2 * 1024 * 1024) {
                this.errors.image = 'File size must be less than 2MB';
                event.target.value = '';
                this.selectedFile = null;
                return;
            }
            
            // Show image preview
            if (this.selectedFile) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.$refs.imagePreview.src = e.target.result;
                };
                reader.readAsDataURL(this.selectedFile);
            }
        },

        validateForm() {
            this.errors = {};
            let isValid = true;

            // Validate required fields
            if (!this.formData.name || this.formData.name.trim() === '') {
                this.errors.name = 'The name field is required.';
                isValid = false;
            }

            // Validate transaction code - only validate if provided
            if (this.formData.transaction_code !== '' && this.formData.transaction_code !== null) {
                if (isNaN(this.formData.transaction_code) || this.formData.transaction_code < 0 || this.formData.transaction_code > 999) {
                    this.errors.transaction_code = 'The transaction code must be between 0 and 999.';
                    isValid = false;
                }
            }

            // Validate status
            if (!this.formData.status) {
                this.errors.status = 'The status field is required.';
                isValid = false;
            }

            return isValid;
        },

        closeDialog() {
            this.showDialog = false;
            this.selectedFile = null;

            // Reset file input
            if (this.$refs.fileInput) {
                this.$refs.fileInput.value = '';
            }
        },

        async submitForm() {
            // Validate form before submission
            if (!this.validateForm()) {
                // Scroll to the first error
                const firstErrorElement = document.querySelector('.text-red-500');
                if (firstErrorElement) {
                    firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return;
            }

            this.loading = true;
            this.errors = {};

            const formData = new FormData();
            formData.append('name', this.formData.name);
            formData.append('description', this.formData.description);
            formData.append('transaction_code', this.formData.transaction_code);

            // Add the new fields - always include them in the form data
            // even if they're empty, so they can be cleared if needed
            formData.append('target', this.formData.target || '');
            formData.append('end_at', this.formData.end_at || '');
            formData.append('achievement', 0); // Always set achievement to 0

            // For non-admin users, don't allow setting status to draft
            @if(auth()->user()->isAdmin())
            formData.append('status', this.formData.status);
            @else
            // Non-admin users can only set status to ongoing or done, not draft
            formData.append('status', this.formData.status === 'draft' ? 'ongoing' : this.formData.status);
            @endif

            if (this.selectedFile) {
                formData.append('image', this.selectedFile);
            }

            try {
                let url = '/program';
                let method = 'POST';

                if (this.formData.id) {
                    url = `/program/${this.formData.id}`;
                    method = 'POST';
                    formData.append('_method', 'PUT'); // Laravel method spoofing
                }

                const response = await fetch(url, {
                    method: method,
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    }
                });

                const result = await response.json();

                if (!response.ok) {
                    if (response.status === 422) {
                        this.errors = result.errors;
                    } else {
                        throw new Error(result.message || 'An error occurred');
                    }
                } else {
                    this.closeDialog();
                    this.loadPage(this.currentPageUrl);
                }
            } catch (error) {
                console.error('Error submitting form:', error);
                alert('An error occurred while saving the program');
            } finally {
                this.loading = false;
            }
        },

        confirmDelete(id) {
            this.currentProgramId = id;
            this.showDeleteDialog = true;
        },

        async deleteProgram() {
            try {
                this.loading = true;

                const response = await fetch(`/program/${this.currentProgramId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.message) {
                    this.showDeleteDialog = false;
                    this.loadPage(this.currentPageUrl);
                } else {
                    alert(result.message || 'Failed to delete program');
                }
            } catch (error) {
                console.error('Error deleting program:', error);
                alert('An error occurred while deleting the program');
            } finally {
                this.loading = false;
            }
        },

        validateNumberInput(event) {
            // Allow Enter key for form submission
            if (event.key === 'Enter') {
                // Let the event propagate to the @keydown.enter handler
                return true;
            }

            // Allow navigation and editing keys
            const allowedKeys = [
                'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
                'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
                'Home', 'End', 'PageUp', 'PageDown'
            ];

            // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
            if (event.ctrlKey || event.metaKey) {
                return true;
            }

            // Allow navigation and editing keys
            if (allowedKeys.includes(event.key)) {
                return true;
            }

            // Allow only numeric characters (0-9)
            if (!/^[0-9]$/.test(event.key)) {
                event.preventDefault();
                return false;
            }

            // Check if the resulting value would be greater than 999
            const input = event.target;
            const currentValue = input.value || '';
            const selectionStart = input.selectionStart || 0;
            const selectionEnd = input.selectionEnd || 0;

            // Calculate what the new value would be
            const newValue = currentValue.substring(0, selectionStart) + event.key + currentValue.substring(selectionEnd);

            // If the new value is greater than 999, prevent the input
            if (parseInt(newValue, 10) > 999) {
                event.preventDefault();
                return false;
            }

            return true;
        },

        applyFilters(event) {
            // Build the URL with filter parameters
            const params = new URLSearchParams();

            if (this.filters.name) {
                params.append('name', this.filters.name);
            }

            if (this.filters.transaction_code !== '' && this.filters.transaction_code !== null) {
                params.append('transaction_code', this.filters.transaction_code);
            }

            if (this.filters.status) {
                params.append('status', this.filters.status);
            }

            // Create the URL with filters
            const url = window.location.pathname + (params.toString() ? '?' + params.toString() : '');

            // Load the filtered page
            this.loadPage(url);

            // If this was triggered by an Enter keypress, prevent default form submission
            if (event && event.type === 'keydown' && event.key === 'Enter') {
                event.preventDefault();
            }
        },


    };
}
</script>
@endsection
